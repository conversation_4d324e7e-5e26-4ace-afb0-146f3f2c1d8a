﻿using System.Text;

namespace CSharpLib.Tests;

public class CalculatorTests
{
    private readonly Calculator _calculator;

    public CalculatorTests()
    {
        _calculator = new Calculator();
    }

    [Fact]
    public void Add_TwoPositiveNumbers_ReturnsCorrectSum()
    {
        // Arrange
        int a = 5;
        int b = 3;
        int expected = 8;

        // Act
        int result = _calculator.Add(a, b);

        // Assert
        Assert.Equal(expected, result);
    }

    [Fact]
    public void Add_PositiveAndNegativeNumber_ReturnsCorrectSum()
    {
        // Arrange
        int a = 10;
        int b = -3;
        int expected = 7;

        // Act
        int result = _calculator.Add(a, b);

        // Assert
        Assert.Equal(expected, result);
    }

    [Fact]
    public void Subtract_TwoPositiveNumbers_ReturnsCorrectDifference()
    {
        // Arrange
        int a = 10;
        int b = 3;
        int expected = 7;

        // Act
        int result = _calculator.Subtract(a, b);

        // Assert
        Assert.Equal(expected, result);
    }

    [Fact]
    public void Multiply_TwoPositiveNumbers_ReturnsCorrectProduct()
    {
        // Arrange
        int a = 4;
        int b = 5;
        int expected = 20;

        // Act
        int result = _calculator.Multiply(a, b);

        // Assert
        Assert.Equal(expected, result);
    }

    [Fact]
    public void Multiply_ByZero_ReturnsZero()
    {
        // Arrange
        int a = 5;
        int b = 0;
        int expected = 0;

        // Act
        int result = _calculator.Multiply(a, b);

        // Assert
        Assert.Equal(expected, result);
    }

    [Fact]
    public void Divide_TwoPositiveNumbers_ReturnsCorrectQuotient()
    {
        // Arrange
        int a = 10;
        int b = 2;
        double expected = 5.0;

        // Act
        double result = _calculator.Divide(a, b);

        // Assert
        Assert.Equal(expected, result);
    }

    [Fact]
    public void Divide_WithRemainder_ReturnsCorrectQuotient()
    {
        // Arrange
        int a = 7;
        int b = 2;
        double expected = 3.5;

        // Act
        double result = _calculator.Divide(a, b);

        // Assert
        Assert.Equal(expected, result);
    }

    [Fact]
    public void Divide_ByZero_ThrowsArgumentException()
    {
        // Arrange
        int a = 5;
        int b = 0;

        // Act & Assert
        Assert.Throws<ArgumentException>(() => _calculator.Divide(a, b));
    }

    [Fact]
    public void Calculator_RaisesCalculationCompletedEvent()
    {
        // Arrange
        bool eventRaised = false;
        string? operationName = null;
        double result = 0;

        _calculator.CalculationCompleted += (sender, e) =>
        {
            eventRaised = true;
            operationName = e.Operation;
            result = e.Result;
        };

        // Act
        _calculator.Add(5, 3);

        // Assert
        Assert.True(eventRaised);
        Assert.Equal("Add", operationName);
        Assert.Equal(8, result);
    }
}

public class FileProcessorTests
{
    private readonly FileProcessor _fileProcessor;
    private readonly string _testFilePath;
    private readonly string _testBinaryFilePath;

    public FileProcessorTests()
    {
        _fileProcessor = new FileProcessor();
        _testFilePath = Path.GetTempFileName();
        _testBinaryFilePath = Path.GetTempFileName();
    }

    [Fact]
    public async Task ReadWriteFileAsync_WorksCorrectly()
    {
        // Arrange
        string testContent = "Hello, World! 测试内容";

        // Act
        await _fileProcessor.WriteFileAsync(_testFilePath, testContent);
        string readContent = await _fileProcessor.ReadFileAsync(_testFilePath);

        // Assert
        Assert.Equal(testContent, readContent);

        // Cleanup
        File.Delete(_testFilePath);
    }

    [Fact]
    public async Task ReadWriteBinaryFileAsync_WorksCorrectly()
    {
        // Arrange
        byte[] testData = Encoding.UTF8.GetBytes("Binary test data 二进制测试数据");

        // Act
        await _fileProcessor.WriteBinaryFileAsync(_testBinaryFilePath, testData);
        byte[] readData = await _fileProcessor.ReadBinaryFileAsync(_testBinaryFilePath);

        // Assert
        Assert.Equal(testData, readData);

        // Cleanup
        File.Delete(_testBinaryFilePath);
    }

    [Fact]
    public async Task ReadFileAsync_FileNotFound_ThrowsException()
    {
        // Arrange
        string nonExistentFile = "non_existent_file.txt";

        // Act & Assert
        await Assert.ThrowsAsync<FileNotFoundException>(() =>
            _fileProcessor.ReadFileAsync(nonExistentFile));
    }
}

public class BinaryProcessorTests
{
    private readonly BinaryProcessor _binaryProcessor;

    public BinaryProcessorTests()
    {
        _binaryProcessor = new BinaryProcessor();
    }

    [Fact]
    public void EncodeDecodeString_WorksCorrectly()
    {
        // Arrange
        string testString = "Hello, 世界! 🌍";

        // Act
        byte[] encoded = _binaryProcessor.EncodeString(testString);
        string decoded = _binaryProcessor.DecodeBytes(encoded);

        // Assert
        Assert.Equal(testString, decoded);
    }

    [Fact]
    public void CompressDecompressData_WorksCorrectly()
    {
        // Arrange
        string testData = string.Join("", Enumerable.Repeat("Test data for compression. ", 100));
        byte[] originalData = Encoding.UTF8.GetBytes(testData);

        // Act
        byte[] compressed = _binaryProcessor.CompressData(originalData);
        byte[] decompressed = _binaryProcessor.DecompressData(compressed);

        // Assert
        Assert.Equal(originalData, decompressed);
        Assert.True(compressed.Length < originalData.Length); // Should be compressed
    }

    [Fact]
    public void CalculateHash_ProducesSameHashForSameData()
    {
        // Arrange
        byte[] data1 = Encoding.UTF8.GetBytes("Test data");
        byte[] data2 = Encoding.UTF8.GetBytes("Test data");
        byte[] data3 = Encoding.UTF8.GetBytes("Different data");

        // Act
        string hash1 = _binaryProcessor.CalculateHash(data1);
        string hash2 = _binaryProcessor.CalculateHash(data2);
        string hash3 = _binaryProcessor.CalculateHash(data3);

        // Assert
        Assert.Equal(hash1, hash2);
        Assert.NotEqual(hash1, hash3);
        Assert.Equal(64, hash1.Length); // SHA256 produces 64 hex characters
    }
}

public class ParallelCalculatorTests
{
    private readonly ParallelCalculator _parallelCalculator;

    public ParallelCalculatorTests()
    {
        _parallelCalculator = new ParallelCalculator();
    }

    [Fact]
    public async Task ParallelCalculateAsync_WorksCorrectly()
    {
        // Arrange
        int[] numbers = { 1, 2, 3, 4, 5 };
        Func<int, int> squareOperation = x => x * x;

        // Act
        int[] results = await _parallelCalculator.ParallelCalculateAsync(numbers, squareOperation);

        // Assert
        Assert.Equal(new[] { 1, 4, 9, 16, 25 }, results);
    }

    [Fact]
    public void ParallelCalculateSync_WorksCorrectly()
    {
        // Arrange
        int[] numbers = { 1, 2, 3, 4, 5 };
        Func<int, int> doubleOperation = x => x * 2;

        // Act
        int[] results = _parallelCalculator.ParallelCalculateSync(numbers, doubleOperation);

        // Assert
        Assert.Equal(new[] { 2, 4, 6, 8, 10 }, results);
    }

    [Fact]
    public async Task CalculateSumConcurrentlyAsync_WorksCorrectly()
    {
        // Arrange
        var numbers = Enumerable.Range(1, 1000);

        // Act
        long result = await _parallelCalculator.CalculateSumConcurrentlyAsync(numbers);

        // Assert
        long expectedSum = numbers.Sum(x => (long)x);
        Assert.Equal(expectedSum, result);
    }
}

public class DataProcessorTests
{
    private readonly DataProcessor _dataProcessor;

    public DataProcessorTests()
    {
        _dataProcessor = new DataProcessor();
    }

    [Fact]
    public void SerializeDeserializeJson_WorksCorrectly()
    {
        // Arrange
        var testObject = new { Name = "Test", Value = 42, Items = new[] { 1, 2, 3 } };

        // Act
        string json = _dataProcessor.SerializeToJson(testObject);
        var deserializedObject = _dataProcessor.DeserializeFromJson<dynamic>(json);

        // Assert
        Assert.NotNull(json);
        Assert.Contains("Test", json);
        Assert.Contains("42", json);
    }

    [Fact]
    public async Task SaveLoadJsonFile_WorksCorrectly()
    {
        // Arrange
        var testData = new { Message = "Hello", Count = 123 };
        string tempFile = Path.GetTempFileName();

        try
        {
            // Act
            await _dataProcessor.SaveToJsonFileAsync(tempFile, testData);
            var loadedData = await _dataProcessor.LoadFromJsonFileAsync<dynamic>(tempFile);

            // Assert
            Assert.NotNull(loadedData);
        }
        finally
        {
            // Cleanup
            if (File.Exists(tempFile))
                File.Delete(tempFile);
        }
    }
}

public class ThreadSafeCounterTests
{
    [Fact]
    public void ThreadSafeCounter_ConcurrentOperations_WorksCorrectly()
    {
        // Arrange
        var counter = new ThreadSafeCounter();
        const int numberOfThreads = 10;
        const int operationsPerThread = 1000;

        // Act
        var tasks = new Task[numberOfThreads];
        for (int i = 0; i < numberOfThreads; i++)
        {
            tasks[i] = Task.Run(() =>
            {
                for (int j = 0; j < operationsPerThread; j++)
                {
                    counter.Increment();
                }
            });
        }

        Task.WaitAll(tasks);

        // Assert
        Assert.Equal(numberOfThreads * operationsPerThread, counter.Count);
    }

    [Fact]
    public void ThreadSafeCounter_IncrementDecrement_WorksCorrectly()
    {
        // Arrange
        var counter = new ThreadSafeCounter();

        // Act
        counter.Increment();
        counter.Increment();
        counter.Decrement();
        counter.Add(5);

        // Assert
        Assert.Equal(6, counter.Count);

        // Act
        counter.Reset();

        // Assert
        Assert.Equal(0, counter.Count);
    }
}

public class PerformanceMonitorTests
{
    private readonly PerformanceMonitor _performanceMonitor;

    public PerformanceMonitorTests()
    {
        _performanceMonitor = new PerformanceMonitor();
    }

    [Fact]
    public void MeasureExecutionTime_ReturnsPositiveTime()
    {
        // Act
        var executionTime = _performanceMonitor.MeasureExecutionTime(() =>
        {
            Thread.Sleep(10); // Simulate work
        });

        // Assert
        Assert.True(executionTime.TotalMilliseconds >= 10);
    }

    [Fact]
    public async Task MeasureExecutionTimeAsync_ReturnsPositiveTime()
    {
        // Act
        var executionTime = await _performanceMonitor.MeasureExecutionTimeAsync(async () =>
        {
            await Task.Delay(10); // Simulate async work
        });

        // Assert
        Assert.True(executionTime.TotalMilliseconds >= 10);
    }

    [Fact]
    public void GetMemoryUsage_ReturnsPositiveValue()
    {
        // Act
        long memoryUsage = _performanceMonitor.GetMemoryUsage();

        // Assert
        Assert.True(memoryUsage > 0);
    }

    [Fact]
    public void ForceGarbageCollection_DoesNotThrow()
    {
        // Act & Assert
        _performanceMonitor.ForceGarbageCollection();
        // If we get here without exception, the test passes
        Assert.True(true);
    }
}
