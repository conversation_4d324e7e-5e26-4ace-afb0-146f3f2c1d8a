const {
    AdvancedCalculator,
    FileProcessor,
    BinaryProcessor,
    HttpClient,
    ParallelProcessor,
    ThreadSafeCounter,
    DataProcessor,
    PerformanceMonitor,
    StreamProcessor,
    asyncFactorial,
    asyncFibonacci
} = require('./advanced-features');

const fs = require('fs');
const path = require('path');
const os = require('os');

// Simple test framework
function test(description, testFn) {
    return new Promise(async (resolve) => {
        try {
            await testFn();
            console.log(`✅ ${description}`);
            resolve(true);
        } catch (error) {
            console.log(`❌ ${description}: ${error.message}`);
            resolve(false);
        }
    });
}

function expect(actual) {
    return {
        toBe: (expected) => {
            if (actual !== expected) {
                throw new Error(`Expected ${expected}, but got ${actual}`);
            }
        },
        toBeCloseTo: (expected, precision = 2) => {
            const diff = Math.abs(actual - expected);
            const tolerance = Math.pow(10, -precision) / 2;
            if (diff > tolerance) {
                throw new Error(`Expected ${actual} to be close to ${expected}`);
            }
        },
        toThrow: async (expectedMessage) => {
            let threw = false;
            try {
                if (typeof actual === 'function') {
                    await actual();
                } else {
                    await actual;
                }
            } catch (error) {
                threw = true;
                if (expectedMessage && !error.message.includes(expectedMessage)) {
                    throw new Error(`Expected error message to contain "${expectedMessage}", but got "${error.message}"`);
                }
            }
            if (!threw) {
                throw new Error('Expected function to throw an error');
            }
        },
        toBeGreaterThan: (expected) => {
            if (actual <= expected) {
                throw new Error(`Expected ${actual} to be greater than ${expected}`);
            }
        },
        toBeInstanceOf: (expectedClass) => {
            if (!(actual instanceof expectedClass)) {
                throw new Error(`Expected ${actual} to be instance of ${expectedClass.name}`);
            }
        },
        toContain: (expected) => {
            if (!actual.includes(expected)) {
                throw new Error(`Expected ${actual} to contain ${expected}`);
            }
        }
    };
}

// Run tests
async function runAdvancedTests() {
    console.log('🚀 Running Advanced JavaScript Tests...\n');
    
    const results = [];
    
    // Advanced Calculator Tests
    console.log('🧮 Advanced Calculator Tests:');
    
    results.push(await test('should emit events on calculation', async () => {
        const calc = new AdvancedCalculator();
        let eventReceived = null;
        
        calc.on('calculation', (event) => {
            eventReceived = event;
        });
        
        const result = calc.add(5, 3);
        
        expect(result).toBe(8);
        expect(eventReceived).toBeInstanceOf(Object);
        expect(eventReceived.operation).toBe('add');
        expect(eventReceived.result).toBe(8);
    }));
    
    results.push(await test('should track calculation history', async () => {
        const calc = new AdvancedCalculator();
        
        calc.add(5, 3);
        calc.multiply(4, 2);
        
        const history = calc.getHistory();
        expect(history.length).toBe(2);
        expect(history[0].operation).toBe('add');
        expect(history[1].operation).toBe('multiply');
    }));
    
    // File Processing Tests
    console.log('\n📁 File Processing Tests:');
    
    results.push(await test('should handle async file operations', async () => {
        const tempFile = path.join(os.tmpdir(), 'test-async.txt');
        const testContent = 'Hello, async world! 测试内容';
        
        try {
            await FileProcessor.writeFileAsync(tempFile, testContent);
            const readContent = await FileProcessor.readFileAsync(tempFile);
            
            expect(readContent).toBe(testContent);
        } finally {
            if (await FileProcessor.fileExists(tempFile)) {
                await fs.promises.unlink(tempFile);
            }
        }
    }));
    
    results.push(await test('should handle binary file operations', async () => {
        const tempFile = path.join(os.tmpdir(), 'test-binary.bin');
        const testData = Buffer.from([0x48, 0x65, 0x6c, 0x6c, 0x6f, 0x00, 0x01, 0x02]);
        
        try {
            await FileProcessor.writeBinaryFileAsync(tempFile, testData);
            const readData = await FileProcessor.readBinaryFileAsync(tempFile);
            
            expect(Buffer.compare(testData, readData)).toBe(0);
        } finally {
            if (await FileProcessor.fileExists(tempFile)) {
                await fs.promises.unlink(tempFile);
            }
        }
    }));
    
    // Binary Processing Tests
    console.log('\n🔢 Binary Processing Tests:');
    
    results.push(await test('should encode and decode strings', async () => {
        const testString = 'Hello, 世界! 🌍';
        
        const encoded = BinaryProcessor.encodeString(testString);
        const decoded = BinaryProcessor.decodeBuffer(encoded);
        
        expect(decoded).toBe(testString);
        expect(encoded).toBeInstanceOf(Buffer);
    }));
    
    results.push(await test('should compress and decompress data', async () => {
        const testData = Buffer.from('This is test data for compression. '.repeat(100));
        
        const compressed = await BinaryProcessor.compressData(testData);
        const decompressed = await BinaryProcessor.decompressData(compressed);
        
        expect(Buffer.compare(testData, decompressed)).toBe(0);
        expect(compressed.length).toBeGreaterThan(0);
    }));
    
    results.push(await test('should calculate hash correctly', async () => {
        const testData1 = Buffer.from('Test data');
        const testData2 = Buffer.from('Test data');
        const testData3 = Buffer.from('Different data');
        
        const hash1 = BinaryProcessor.calculateHash(testData1);
        const hash2 = BinaryProcessor.calculateHash(testData2);
        const hash3 = BinaryProcessor.calculateHash(testData3);
        
        expect(hash1).toBe(hash2);
        expect(hash1 === hash3).toBe(false);
        expect(hash1.length).toBe(64); // SHA256 produces 64 hex characters
    }));
    
    // Parallel Processing Tests
    console.log('\n⚡ Parallel Processing Tests:');
    
    results.push(await test('should process items in parallel', async () => {
        const numbers = [1, 2, 3, 4, 5];
        
        const asyncSquare = async (x) => {
            await new Promise(resolve => setTimeout(resolve, 10));
            return x * x;
        };
        
        const results = await ParallelProcessor.parallelMap(numbers, asyncSquare, 2);
        const expected = [1, 4, 9, 16, 25];
        
        expect(JSON.stringify(results)).toBe(JSON.stringify(expected));
    }));
    
    results.push(await test('should calculate sum in parallel', async () => {
        const numbers = Array.from({length: 1000}, (_, i) => i + 1);
        const expectedSum = numbers.reduce((sum, num) => sum + num, 0);
        
        const parallelSum = await ParallelProcessor.calculateSumParallel(numbers, 100);
        
        expect(parallelSum).toBe(expectedSum);
    }));
    
    // Thread-Safe Counter Tests
    console.log('\n🔒 Thread-Safe Counter Tests:');
    
    results.push(await test('should handle concurrent operations', async () => {
        const counter = new ThreadSafeCounter();
        const operations = [];
        
        // Simulate concurrent increments
        for (let i = 0; i < 10; i++) {
            operations.push(counter.increment());
        }
        
        await Promise.all(operations);
        
        expect(counter.getValue()).toBe(10);
    }));
    
    // Data Processing Tests
    console.log('\n📊 Data Processing Tests:');
    
    results.push(await test('should serialize and deserialize JSON', async () => {
        const testData = {
            name: 'Test',
            value: 42,
            items: [1, 2, 3],
            nested: { key: 'value' }
        };
        
        const jsonStr = DataProcessor.serializeToJson(testData);
        const deserialized = DataProcessor.deserializeFromJson(jsonStr);
        
        expect(JSON.stringify(testData)).toBe(JSON.stringify(deserialized));
    }));
    
    results.push(await test('should handle JSON file operations', async () => {
        const tempFile = path.join(os.tmpdir(), 'test-data.json');
        const testData = { message: 'Hello', count: 123 };
        
        try {
            await DataProcessor.saveToJsonFile(tempFile, testData);
            const loadedData = await DataProcessor.loadFromJsonFile(tempFile);
            
            expect(JSON.stringify(testData)).toBe(JSON.stringify(loadedData));
        } finally {
            if (await FileProcessor.fileExists(tempFile)) {
                await fs.promises.unlink(tempFile);
            }
        }
    }));
    
    // Performance Monitoring Tests
    console.log('\n⏱️ Performance Monitoring Tests:');
    
    results.push(await test('should measure execution time', async () => {
        const slowFunction = () => {
            const start = Date.now();
            while (Date.now() - start < 50) {
                // Busy wait for 50ms
            }
            return 'result';
        };
        
        const { result, executionTime } = PerformanceMonitor.measureExecutionTime(slowFunction);
        
        expect(result).toBe('result');
        expect(executionTime).toBeGreaterThan(40); // Should be at least 40ms
    }));
    
    results.push(await test('should measure async execution time', async () => {
        const slowAsyncFunction = async () => {
            await new Promise(resolve => setTimeout(resolve, 50));
            return 'async result';
        };
        
        const { result, executionTime } = await PerformanceMonitor.measureAsyncExecutionTime(slowAsyncFunction);
        
        expect(result).toBe('async result');
        expect(executionTime).toBeGreaterThan(40); // Should be at least 40ms
    }));
    
    results.push(await test('should get memory usage', async () => {
        const memoryUsage = PerformanceMonitor.getMemoryUsage();
        
        expect(memoryUsage).toBeInstanceOf(Object);
        expect(memoryUsage.heapUsed).toBeGreaterThan(0);
        expect(memoryUsage.heapTotal).toBeGreaterThan(0);
    }));
    
    // Async Function Tests
    console.log('\n🔄 Async Function Tests:');
    
    results.push(await test('should calculate async factorial', async () => {
        const result = await asyncFactorial(5);
        expect(result).toBe(120);
        
        const result0 = await asyncFactorial(0);
        expect(result0).toBe(1);
    }));
    
    results.push(await test('should calculate async fibonacci', async () => {
        const result = await asyncFibonacci(5);
        expect(result).toBe(5);
        
        const result0 = await asyncFibonacci(0);
        expect(result0).toBe(0);
        
        const result1 = await asyncFibonacci(1);
        expect(result1).toBe(1);
    }));
    
    // Summary
    const passed = results.filter(r => r).length;
    const total = results.length;
    
    console.log('\n🎉 Advanced JavaScript tests completed!');
    console.log(`Tests passed: ${passed}/${total}`);
    
    if (passed === total) {
        console.log('🎊 All tests passed!');
    } else {
        console.log('❌ Some tests failed.');
    }
    
    return { passed, total };
}

// Run the tests
if (require.main === module) {
    runAdvancedTests().catch(console.error);
}

module.exports = { runAdvancedTests };
