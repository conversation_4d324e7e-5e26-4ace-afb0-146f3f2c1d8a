import unittest
import asyncio
import threading
import time
import tempfile
import os
from pathlib import Path
from advanced_features import (
    AdvancedCalculator, FileProcessor, BinaryProcessor, HttpDownloader,
    ParallelProcessor, ThreadSafeCounter, DataProcessor, PerformanceMonitor,
    async_factorial, async_<PERSON><PERSON><PERSON><PERSON>, CalculationEvent
)


class TestAdvancedCalculator(unittest.TestCase):
    
    def setUp(self):
        self.calc = AdvancedCalculator()
        self.events_received = []
    
    def test_event_emission(self):
        """Test that calculator emits events"""
        def on_calculation(event):
            self.events_received.append(event)
        
        self.calc.on('calculation', on_calculation)
        result = self.calc.add(5, 3)
        
        self.assertEqual(result, 8)
        self.assertEqual(len(self.events_received), 1)
        self.assertEqual(self.events_received[0].operation, 'add')
        self.assertEqual(self.events_received[0].result, 8)
    
    def test_calculation_history(self):
        """Test calculation history tracking"""
        self.calc.add(5, 3)
        self.calc.multiply(4, 2)
        
        self.assertEqual(len(self.calc.history), 2)
        self.assertEqual(self.calc.history[0].operation, 'add')
        self.assertEqual(self.calc.history[1].operation, 'multiply')


class TestFileProcessor(unittest.TestCase):
    
    def setUp(self):
        self.temp_dir = tempfile.mkdtemp()
        self.test_file = os.path.join(self.temp_dir, 'test.txt')
        self.binary_file = os.path.join(self.temp_dir, 'test.bin')
    
    def tearDown(self):
        # Clean up temp files
        for file_path in [self.test_file, self.binary_file]:
            if os.path.exists(file_path):
                os.remove(file_path)
        os.rmdir(self.temp_dir)
    
    def test_async_file_operations(self):
        """Test async file read/write"""
        async def test_async():
            test_content = "Hello, async world! 测试内容"
            
            await FileProcessor.write_file_async(self.test_file, test_content)
            read_content = await FileProcessor.read_file_async(self.test_file)
            
            self.assertEqual(test_content, read_content)
        
        asyncio.run(test_async())
    
    def test_binary_file_operations(self):
        """Test binary file operations"""
        test_data = b"Binary test data \x00\x01\x02\x03"
        
        FileProcessor.write_binary_file(self.binary_file, test_data)
        read_data = FileProcessor.read_binary_file(self.binary_file)
        
        self.assertEqual(test_data, read_data)


class TestBinaryProcessor(unittest.TestCase):
    
    def test_string_encoding_decoding(self):
        """Test string encoding and decoding"""
        test_string = "Hello, 世界! 🌍"
        
        encoded = BinaryProcessor.encode_string(test_string)
        decoded = BinaryProcessor.decode_bytes(encoded)
        
        self.assertEqual(test_string, decoded)
        self.assertIsInstance(encoded, bytes)
    
    def test_data_compression(self):
        """Test data compression and decompression"""
        test_data = b"This is test data for compression. " * 100
        
        compressed = BinaryProcessor.compress_data(test_data)
        decompressed = BinaryProcessor.decompress_data(compressed)
        
        self.assertEqual(test_data, decompressed)
        self.assertLess(len(compressed), len(test_data))  # Should be compressed
    
    def test_hash_calculation(self):
        """Test hash calculation"""
        test_data1 = b"Test data"
        test_data2 = b"Test data"
        test_data3 = b"Different data"
        
        hash1 = BinaryProcessor.calculate_hash(test_data1)
        hash2 = BinaryProcessor.calculate_hash(test_data2)
        hash3 = BinaryProcessor.calculate_hash(test_data3)
        
        self.assertEqual(hash1, hash2)
        self.assertNotEqual(hash1, hash3)
        self.assertEqual(len(hash1), 64)  # SHA256 produces 64 hex characters


class TestParallelProcessor(unittest.TestCase):
    
    def test_parallel_map(self):
        """Test parallel mapping function"""
        numbers = [1, 2, 3, 4, 5]
        
        def square(x):
            time.sleep(0.01)  # Simulate work
            return x * x
        
        results = ParallelProcessor.parallel_map(square, numbers, max_workers=2)
        expected = [1, 4, 9, 16, 25]
        
        self.assertEqual(results, expected)
    
    def test_async_parallel_map(self):
        """Test async parallel mapping"""
        async def test_async():
            numbers = [1, 2, 3, 4, 5]
            
            async def async_square(x):
                await asyncio.sleep(0.01)  # Simulate async work
                return x * x
            
            results = await ParallelProcessor.async_parallel_map(async_square, numbers)
            expected = [1, 4, 9, 16, 25]
            
            self.assertEqual(results, expected)
        
        asyncio.run(test_async())
    
    def test_parallel_sum_calculation(self):
        """Test parallel sum calculation"""
        numbers = list(range(1, 1001))  # 1 to 1000
        expected_sum = sum(numbers)
        
        parallel_sum = ParallelProcessor.calculate_sum_parallel(numbers, num_threads=4)
        
        self.assertEqual(parallel_sum, expected_sum)


class TestThreadSafeCounter(unittest.TestCase):
    
    def test_thread_safe_operations(self):
        """Test thread-safe counter operations"""
        counter = ThreadSafeCounter()
        num_threads = 10
        operations_per_thread = 1000
        
        def increment_worker():
            for _ in range(operations_per_thread):
                counter.increment()
        
        threads = []
        for _ in range(num_threads):
            thread = threading.Thread(target=increment_worker)
            threads.append(thread)
            thread.start()
        
        for thread in threads:
            thread.join()
        
        expected_value = num_threads * operations_per_thread
        self.assertEqual(counter.get_value(), expected_value)
    
    def test_counter_operations(self):
        """Test basic counter operations"""
        counter = ThreadSafeCounter()
        
        counter.increment()
        counter.increment()
        self.assertEqual(counter.get_value(), 2)
        
        counter.decrement()
        self.assertEqual(counter.get_value(), 1)
        
        counter.reset()
        self.assertEqual(counter.get_value(), 0)


class TestDataProcessor(unittest.TestCase):
    
    def setUp(self):
        self.temp_dir = tempfile.mkdtemp()
        self.json_file = os.path.join(self.temp_dir, 'test.json')
    
    def tearDown(self):
        if os.path.exists(self.json_file):
            os.remove(self.json_file)
        os.rmdir(self.temp_dir)
    
    def test_json_serialization(self):
        """Test JSON serialization and deserialization"""
        test_data = {
            'name': 'Test',
            'value': 42,
            'items': [1, 2, 3],
            'nested': {'key': 'value'}
        }
        
        json_str = DataProcessor.serialize_to_json(test_data)
        deserialized = DataProcessor.deserialize_from_json(json_str)
        
        self.assertEqual(test_data, deserialized)
    
    def test_async_json_file_operations(self):
        """Test async JSON file operations"""
        async def test_async():
            test_data = {'message': 'Hello', 'count': 123}
            
            await DataProcessor.save_to_json_file(self.json_file, test_data)
            loaded_data = await DataProcessor.load_from_json_file(self.json_file)
            
            self.assertEqual(test_data, loaded_data)
        
        asyncio.run(test_async())


class TestPerformanceMonitor(unittest.TestCase):
    
    def test_execution_time_measurement(self):
        """Test execution time measurement"""
        def slow_function():
            time.sleep(0.1)
            return "result"
        
        result, execution_time = PerformanceMonitor.measure_execution_time(slow_function)
        
        self.assertEqual(result, "result")
        self.assertGreaterEqual(execution_time, 0.1)
    
    def test_async_execution_time_measurement(self):
        """Test async execution time measurement"""
        async def test_async():
            async def slow_async_function():
                await asyncio.sleep(0.1)
                return "async result"
            
            result, execution_time = await PerformanceMonitor.measure_async_execution_time(
                slow_async_function()
            )
            
            self.assertEqual(result, "async result")
            self.assertGreaterEqual(execution_time, 0.1)
        
        asyncio.run(test_async())


class TestAsyncFunctions(unittest.TestCase):
    
    def test_async_factorial(self):
        """Test async factorial calculation"""
        async def test_async():
            result = await async_factorial(5)
            self.assertEqual(result, 120)
            
            result = await async_factorial(0)
            self.assertEqual(result, 1)
        
        asyncio.run(test_async())
    
    def test_async_fibonacci(self):
        """Test async fibonacci calculation"""
        async def test_async():
            # Test small numbers to avoid long execution time
            result = await async_fibonacci(5)
            self.assertEqual(result, 5)
            
            result = await async_fibonacci(0)
            self.assertEqual(result, 0)
            
            result = await async_fibonacci(1)
            self.assertEqual(result, 1)
        
        asyncio.run(test_async())


if __name__ == '__main__':
    unittest.main()
