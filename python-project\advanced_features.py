import asyncio
import threading
import time
import json
import gzip
import hashlib
import concurrent.futures
from typing import List, Dict, Any, Callable, Optional
from dataclasses import dataclass
from pathlib import Path
import urllib.request
import urllib.error


@dataclass
class CalculationEvent:
    operation: str
    result: float
    timestamp: float


class EventEmitter:
    """Simple event emitter for Python"""
    def __init__(self):
        self._listeners = {}
    
    def on(self, event: str, callback: Callable):
        if event not in self._listeners:
            self._listeners[event] = []
        self._listeners[event].append(callback)
    
    def emit(self, event: str, *args, **kwargs):
        if event in self._listeners:
            for callback in self._listeners[event]:
                callback(*args, **kwargs)


class AdvancedCalculator(EventEmitter):
    """Calculator with event handling capabilities"""
    
    def __init__(self):
        super().__init__()
        self.history = []
    
    def add(self, a: float, b: float) -> float:
        result = a + b
        event = CalculationEvent("add", result, time.time())
        self.history.append(event)
        self.emit("calculation", event)
        return result
    
    def multiply(self, a: float, b: float) -> float:
        result = a * b
        event = CalculationEvent("multiply", result, time.time())
        self.history.append(event)
        self.emit("calculation", event)
        return result


class FileProcessor:
    """Async file operations and binary processing"""
    
    @staticmethod
    async def read_file_async(file_path: str) -> str:
        """Simulate async file reading"""
        await asyncio.sleep(0.01)  # Simulate I/O delay
        with open(file_path, 'r', encoding='utf-8') as f:
            return f.read()
    
    @staticmethod
    async def write_file_async(file_path: str, content: str) -> None:
        """Simulate async file writing"""
        await asyncio.sleep(0.01)  # Simulate I/O delay
        with open(file_path, 'w', encoding='utf-8') as f:
            f.write(content)
    
    @staticmethod
    def read_binary_file(file_path: str) -> bytes:
        """Read binary file"""
        with open(file_path, 'rb') as f:
            return f.read()
    
    @staticmethod
    def write_binary_file(file_path: str, data: bytes) -> None:
        """Write binary file"""
        with open(file_path, 'wb') as f:
            f.write(data)


class BinaryProcessor:
    """Binary data processing utilities"""
    
    @staticmethod
    def encode_string(text: str, encoding: str = 'utf-8') -> bytes:
        """Encode string to bytes"""
        return text.encode(encoding)
    
    @staticmethod
    def decode_bytes(data: bytes, encoding: str = 'utf-8') -> str:
        """Decode bytes to string"""
        return data.decode(encoding)
    
    @staticmethod
    def compress_data(data: bytes) -> bytes:
        """Compress data using gzip"""
        return gzip.compress(data)
    
    @staticmethod
    def decompress_data(compressed_data: bytes) -> bytes:
        """Decompress gzip data"""
        return gzip.decompress(compressed_data)
    
    @staticmethod
    def calculate_hash(data: bytes, algorithm: str = 'sha256') -> str:
        """Calculate hash of data"""
        hash_obj = hashlib.new(algorithm)
        hash_obj.update(data)
        return hash_obj.hexdigest()


class HttpDownloader:
    """HTTP download utilities"""
    
    @staticmethod
    def download_string(url: str, timeout: int = 30) -> str:
        """Download string content from URL"""
        try:
            with urllib.request.urlopen(url, timeout=timeout) as response:
                return response.read().decode('utf-8')
        except urllib.error.URLError as e:
            raise ConnectionError(f"Failed to download from {url}: {e}")
    
    @staticmethod
    def download_binary(url: str, timeout: int = 30) -> bytes:
        """Download binary content from URL"""
        try:
            with urllib.request.urlopen(url, timeout=timeout) as response:
                return response.read()
        except urllib.error.URLError as e:
            raise ConnectionError(f"Failed to download binary from {url}: {e}")


class ParallelProcessor:
    """Parallel and concurrent processing utilities"""
    
    @staticmethod
    def parallel_map(func: Callable, items: List[Any], max_workers: int = 4) -> List[Any]:
        """Apply function to items in parallel using threads"""
        with concurrent.futures.ThreadPoolExecutor(max_workers=max_workers) as executor:
            return list(executor.map(func, items))
    
    @staticmethod
    async def async_parallel_map(func: Callable, items: List[Any]) -> List[Any]:
        """Apply async function to items in parallel"""
        tasks = [asyncio.create_task(func(item)) for item in items]
        return await asyncio.gather(*tasks)
    
    @staticmethod
    def calculate_sum_parallel(numbers: List[int], num_threads: int = 4) -> int:
        """Calculate sum using multiple threads"""
        chunk_size = len(numbers) // num_threads
        chunks = [numbers[i:i + chunk_size] for i in range(0, len(numbers), chunk_size)]
        
        def sum_chunk(chunk):
            return sum(chunk)
        
        with concurrent.futures.ThreadPoolExecutor(max_workers=num_threads) as executor:
            results = list(executor.map(sum_chunk, chunks))
        
        return sum(results)


class ThreadSafeCounter:
    """Thread-safe counter implementation"""
    
    def __init__(self):
        self._value = 0
        self._lock = threading.Lock()
    
    def increment(self):
        with self._lock:
            self._value += 1
    
    def decrement(self):
        with self._lock:
            self._value -= 1
    
    def get_value(self):
        with self._lock:
            return self._value
    
    def reset(self):
        with self._lock:
            self._value = 0


class DataProcessor:
    """JSON and data processing utilities"""
    
    @staticmethod
    def serialize_to_json(obj: Any, indent: int = 2) -> str:
        """Serialize object to JSON string"""
        return json.dumps(obj, indent=indent, ensure_ascii=False)
    
    @staticmethod
    def deserialize_from_json(json_str: str) -> Any:
        """Deserialize JSON string to object"""
        return json.loads(json_str)
    
    @staticmethod
    async def save_to_json_file(file_path: str, obj: Any) -> None:
        """Save object to JSON file asynchronously"""
        json_str = DataProcessor.serialize_to_json(obj)
        await FileProcessor.write_file_async(file_path, json_str)
    
    @staticmethod
    async def load_from_json_file(file_path: str) -> Any:
        """Load object from JSON file asynchronously"""
        json_str = await FileProcessor.read_file_async(file_path)
        return DataProcessor.deserialize_from_json(json_str)


class PerformanceMonitor:
    """Performance monitoring utilities"""
    
    @staticmethod
    def measure_execution_time(func: Callable, *args, **kwargs) -> tuple:
        """Measure execution time of a function"""
        start_time = time.perf_counter()
        result = func(*args, **kwargs)
        end_time = time.perf_counter()
        execution_time = end_time - start_time
        return result, execution_time
    
    @staticmethod
    async def measure_async_execution_time(coro) -> tuple:
        """Measure execution time of an async function"""
        start_time = time.perf_counter()
        result = await coro
        end_time = time.perf_counter()
        execution_time = end_time - start_time
        return result, execution_time


# Async utilities
async def async_factorial(n: int) -> int:
    """Calculate factorial asynchronously"""
    if n <= 1:
        return 1
    await asyncio.sleep(0.001)  # Simulate async work
    return n * await async_factorial(n - 1)


async def async_fibonacci(n: int) -> int:
    """Calculate fibonacci asynchronously"""
    if n <= 1:
        return n
    await asyncio.sleep(0.001)  # Simulate async work
    return await async_fibonacci(n - 1) + await async_fibonacci(n - 2)
