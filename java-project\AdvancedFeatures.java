import java.io.*;
import java.nio.file.*;
import java.util.*;
import java.util.concurrent.*;
import java.util.concurrent.atomic.AtomicLong;
import java.util.stream.Collectors;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.util.zip.GZIPInputStream;
import java.util.zip.GZIPOutputStream;
import java.net.http.HttpClient;
import java.net.http.HttpRequest;
import java.net.http.HttpResponse;
import java.net.URI;
import java.time.Duration;
import java.time.Instant;

// Event handling
interface CalculationListener {
    void onCalculation(String operation, double result, long timestamp);
}

class AdvancedCalculator {
    private List<CalculationListener> listeners = new ArrayList<>();
    private List<CalculationEvent> history = new ArrayList<>();
    
    public void addListener(CalculationListener listener) {
        listeners.add(listener);
    }
    
    public double add(double a, double b) {
        double result = a + b;
        notifyListeners("add", result);
        return result;
    }
    
    public double multiply(double a, double b) {
        double result = a * b;
        notifyListeners("multiply", result);
        return result;
    }
    
    private void notifyListeners(String operation, double result) {
        long timestamp = System.currentTimeMillis();
        CalculationEvent event = new CalculationEvent(operation, result, timestamp);
        history.add(event);
        
        for (CalculationListener listener : listeners) {
            listener.onCalculation(operation, result, timestamp);
        }
    }
    
    public List<CalculationEvent> getHistory() {
        return new ArrayList<>(history);
    }
    
    static class CalculationEvent {
        final String operation;
        final double result;
        final long timestamp;
        
        CalculationEvent(String operation, double result, long timestamp) {
            this.operation = operation;
            this.result = result;
            this.timestamp = timestamp;
        }
    }
}

// File processing utilities
class FileProcessor {
    public static CompletableFuture<String> readFileAsync(String filePath) {
        return CompletableFuture.supplyAsync(() -> {
            try {
                return Files.readString(Paths.get(filePath));
            } catch (IOException e) {
                throw new RuntimeException("Failed to read file: " + filePath, e);
            }
        });
    }
    
    public static CompletableFuture<Void> writeFileAsync(String filePath, String content) {
        return CompletableFuture.runAsync(() -> {
            try {
                Files.writeString(Paths.get(filePath), content);
            } catch (IOException e) {
                throw new RuntimeException("Failed to write file: " + filePath, e);
            }
        });
    }
    
    public static byte[] readBinaryFile(String filePath) throws IOException {
        return Files.readAllBytes(Paths.get(filePath));
    }
    
    public static void writeBinaryFile(String filePath, byte[] data) throws IOException {
        Files.write(Paths.get(filePath), data);
    }
}

// Binary processing utilities
class BinaryProcessor {
    public static byte[] encodeString(String text) {
        return text.getBytes(java.nio.charset.StandardCharsets.UTF_8);
    }
    
    public static String decodeBytes(byte[] data) {
        return new String(data, java.nio.charset.StandardCharsets.UTF_8);
    }
    
    public static byte[] compressData(byte[] data) throws IOException {
        ByteArrayOutputStream baos = new ByteArrayOutputStream();
        try (GZIPOutputStream gzipOut = new GZIPOutputStream(baos)) {
            gzipOut.write(data);
        }
        return baos.toByteArray();
    }
    
    public static byte[] decompressData(byte[] compressedData) throws IOException {
        ByteArrayInputStream bais = new ByteArrayInputStream(compressedData);
        ByteArrayOutputStream baos = new ByteArrayOutputStream();
        
        try (GZIPInputStream gzipIn = new GZIPInputStream(bais)) {
            byte[] buffer = new byte[1024];
            int len;
            while ((len = gzipIn.read(buffer)) != -1) {
                baos.write(buffer, 0, len);
            }
        }
        return baos.toByteArray();
    }
    
    public static String calculateHash(byte[] data, String algorithm) throws NoSuchAlgorithmException {
        MessageDigest digest = MessageDigest.getInstance(algorithm);
        byte[] hash = digest.digest(data);
        StringBuilder hexString = new StringBuilder();
        
        for (byte b : hash) {
            String hex = Integer.toHexString(0xff & b);
            if (hex.length() == 1) {
                hexString.append('0');
            }
            hexString.append(hex);
        }
        return hexString.toString();
    }
}

// HTTP client utilities
class HttpDownloader {
    private static final HttpClient client = HttpClient.newBuilder()
            .connectTimeout(Duration.ofSeconds(30))
            .build();
    
    public static CompletableFuture<String> downloadStringAsync(String url) {
        HttpRequest request = HttpRequest.newBuilder()
                .uri(URI.create(url))
                .timeout(Duration.ofSeconds(30))
                .build();
        
        return client.sendAsync(request, HttpResponse.BodyHandlers.ofString())
                .thenApply(HttpResponse::body);
    }
    
    public static CompletableFuture<byte[]> downloadBinaryAsync(String url) {
        HttpRequest request = HttpRequest.newBuilder()
                .uri(URI.create(url))
                .timeout(Duration.ofSeconds(30))
                .build();
        
        return client.sendAsync(request, HttpResponse.BodyHandlers.ofByteArray())
                .thenApply(HttpResponse::body);
    }
}

// Parallel processing utilities
class ParallelProcessor {
    private static final ForkJoinPool customThreadPool = new ForkJoinPool(4);
    
    public static <T, R> List<R> parallelMap(List<T> items, java.util.function.Function<T, R> mapper) {
        return items.parallelStream()
                .map(mapper)
                .collect(Collectors.toList());
    }
    
    public static <T, R> CompletableFuture<List<R>> parallelMapAsync(List<T> items, 
            java.util.function.Function<T, CompletableFuture<R>> asyncMapper) {
        List<CompletableFuture<R>> futures = items.stream()
                .map(asyncMapper)
                .collect(Collectors.toList());
        
        return CompletableFuture.allOf(futures.toArray(new CompletableFuture[0]))
                .thenApply(v -> futures.stream()
                        .map(CompletableFuture::join)
                        .collect(Collectors.toList()));
    }
    
    public static long calculateSumParallel(List<Integer> numbers) {
        return numbers.parallelStream()
                .mapToLong(Integer::longValue)
                .sum();
    }
    
    public static CompletableFuture<Long> calculateSumConcurrently(List<Integer> numbers) {
        return CompletableFuture.supplyAsync(() -> {
            return numbers.parallelStream()
                    .mapToLong(Integer::longValue)
                    .sum();
        }, customThreadPool);
    }
}

// Thread-safe counter
class ThreadSafeCounter {
    private final AtomicLong count = new AtomicLong(0);
    
    public void increment() {
        count.incrementAndGet();
    }
    
    public void decrement() {
        count.decrementAndGet();
    }
    
    public void add(long value) {
        count.addAndGet(value);
    }
    
    public long getValue() {
        return count.get();
    }
    
    public void reset() {
        count.set(0);
    }
}

// Performance monitoring
class PerformanceMonitor {
    public static <T> TimedResult<T> measureExecutionTime(java.util.function.Supplier<T> supplier) {
        Instant start = Instant.now();
        T result = supplier.get();
        Instant end = Instant.now();
        long executionTimeMs = Duration.between(start, end).toMillis();
        return new TimedResult<>(result, executionTimeMs);
    }
    
    public static <T> CompletableFuture<TimedResult<T>> measureAsyncExecutionTime(
            java.util.function.Supplier<CompletableFuture<T>> asyncSupplier) {
        Instant start = Instant.now();
        return asyncSupplier.get()
                .thenApply(result -> {
                    Instant end = Instant.now();
                    long executionTimeMs = Duration.between(start, end).toMillis();
                    return new TimedResult<>(result, executionTimeMs);
                });
    }
    
    public static long getMemoryUsage() {
        Runtime runtime = Runtime.getRuntime();
        return runtime.totalMemory() - runtime.freeMemory();
    }
    
    public static void forceGarbageCollection() {
        System.gc();
    }
    
    static class TimedResult<T> {
        final T result;
        final long executionTimeMs;
        
        TimedResult(T result, long executionTimeMs) {
            this.result = result;
            this.executionTimeMs = executionTimeMs;
        }
    }
}

// Async utilities
class AsyncUtils {
    public static CompletableFuture<Long> asyncFactorial(int n) {
        return CompletableFuture.supplyAsync(() -> {
            if (n <= 1) return 1L;
            long result = 1;
            for (int i = 2; i <= n; i++) {
                result *= i;
                // Simulate some async work
                try {
                    Thread.sleep(1);
                } catch (InterruptedException e) {
                    Thread.currentThread().interrupt();
                    throw new RuntimeException(e);
                }
            }
            return result;
        });
    }
    
    public static CompletableFuture<Long> asyncFibonacci(int n) {
        return CompletableFuture.supplyAsync(() -> {
            if (n <= 1) return (long) n;
            
            long a = 0, b = 1;
            for (int i = 2; i <= n; i++) {
                long temp = a + b;
                a = b;
                b = temp;
                // Simulate some async work
                try {
                    Thread.sleep(1);
                } catch (InterruptedException e) {
                    Thread.currentThread().interrupt();
                    throw new RuntimeException(e);
                }
            }
            return b;
        });
    }
}
