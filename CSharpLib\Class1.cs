using System.Collections.Concurrent;
using System.Text;
using System.Text.Json;

namespace CSharpLib;

// Event handling
public class CalculationEventArgs : EventArgs
{
    public string Operation { get; set; } = string.Empty;
    public double Result { get; set; }
    public DateTime Timestamp { get; set; } = DateTime.Now;
}

public class Calculator
{
    // Event for calculation completed
    public event EventHandler<CalculationEventArgs>? CalculationCompleted;

    // Basic operations
    public int Add(int a, int b)
    {
        var result = a + b;
        OnCalculationCompleted("Add", result);
        return result;
    }

    public int Subtract(int a, int b)
    {
        var result = a - b;
        OnCalculationCompleted("Subtract", result);
        return result;
    }

    public int Multiply(int a, int b)
    {
        var result = a * b;
        OnCalculationCompleted("Multiply", result);
        return result;
    }

    public double Divide(int a, int b)
    {
        if (b == 0)
            throw new ArgumentException("Cannot divide by zero");
        var result = (double)a / b;
        OnCalculationCompleted("Divide", result);
        return result;
    }

    protected virtual void OnCalculationCompleted(string operation, double result)
    {
        CalculationCompleted?.Invoke(this, new CalculationEventArgs
        {
            Operation = operation,
            Result = result
        });
    }
}

// Async operations and file handling
public class FileProcessor
{
    public async Task<string> ReadFileAsync(string filePath)
    {
        if (!File.Exists(filePath))
            throw new FileNotFoundException($"File not found: {filePath}");

        return await File.ReadAllTextAsync(filePath);
    }

    public async Task WriteFileAsync(string filePath, string content)
    {
        await File.WriteAllTextAsync(filePath, content);
    }

    public async Task<byte[]> ReadBinaryFileAsync(string filePath)
    {
        if (!File.Exists(filePath))
            throw new FileNotFoundException($"File not found: {filePath}");

        return await File.ReadAllBytesAsync(filePath);
    }

    public async Task WriteBinaryFileAsync(string filePath, byte[] data)
    {
        await File.WriteAllBytesAsync(filePath, data);
    }
}

// HTTP client for downloads
public class HttpDownloader
{
    private readonly HttpClient _httpClient;

    public HttpDownloader()
    {
        _httpClient = new HttpClient();
        _httpClient.Timeout = TimeSpan.FromSeconds(30);
    }

    public async Task<string> DownloadStringAsync(string url)
    {
        try
        {
            var response = await _httpClient.GetAsync(url);
            response.EnsureSuccessStatusCode();
            return await response.Content.ReadAsStringAsync();
        }
        catch (HttpRequestException ex)
        {
            throw new InvalidOperationException($"Failed to download from {url}: {ex.Message}", ex);
        }
    }

    public async Task<byte[]> DownloadBinaryAsync(string url)
    {
        try
        {
            var response = await _httpClient.GetAsync(url);
            response.EnsureSuccessStatusCode();
            return await response.Content.ReadAsByteArrayAsync();
        }
        catch (HttpRequestException ex)
        {
            throw new InvalidOperationException($"Failed to download binary from {url}: {ex.Message}", ex);
        }
    }

    public void Dispose()
    {
        _httpClient?.Dispose();
    }
}

// Threading and parallel operations
public class ParallelCalculator
{
    public async Task<int[]> ParallelCalculateAsync(int[] numbers, Func<int, int> operation)
    {
        var tasks = numbers.Select(async num =>
        {
            await Task.Delay(10); // Simulate work
            return operation(num);
        });

        return await Task.WhenAll(tasks);
    }

    public int[] ParallelCalculateSync(int[] numbers, Func<int, int> operation)
    {
        return numbers.AsParallel().Select(operation).ToArray();
    }

    public async Task<long> CalculateSumConcurrentlyAsync(IEnumerable<int> numbers)
    {
        var numbersList = numbers.ToList();
        var tasks = new List<Task<long>>();
        var chunkSize = Math.Max(1, numbersList.Count / Environment.ProcessorCount);

        await Task.Run(() =>
        {
            for (int i = 0; i < numbersList.Count; i += chunkSize)
            {
                var chunk = numbersList.Skip(i).Take(chunkSize);
                var task = Task.Run(() => chunk.Sum(x => (long)x));
                tasks.Add(task);
            }
        });

        var results = await Task.WhenAll(tasks);
        return results.Sum();
    }
}

// Binary data processing
public class BinaryProcessor
{
    public byte[] EncodeString(string text, Encoding? encoding = null)
    {
        encoding ??= Encoding.UTF8;
        return encoding.GetBytes(text);
    }

    public string DecodeBytes(byte[] data, Encoding? encoding = null)
    {
        encoding ??= Encoding.UTF8;
        return encoding.GetString(data);
    }

    public byte[] CompressData(byte[] data)
    {
        using var output = new MemoryStream();
        using (var gzip = new System.IO.Compression.GZipStream(output, System.IO.Compression.CompressionMode.Compress))
        {
            gzip.Write(data, 0, data.Length);
        }
        return output.ToArray();
    }

    public byte[] DecompressData(byte[] compressedData)
    {
        using var input = new MemoryStream(compressedData);
        using var gzip = new System.IO.Compression.GZipStream(input, System.IO.Compression.CompressionMode.Decompress);
        using var output = new MemoryStream();
        gzip.CopyTo(output);
        return output.ToArray();
    }

    public string CalculateHash(byte[] data)
    {
        using var sha256 = System.Security.Cryptography.SHA256.Create();
        var hash = sha256.ComputeHash(data);
        return Convert.ToHexString(hash);
    }
}

// JSON serialization and data processing
public class DataProcessor
{
    public string SerializeToJson<T>(T obj)
    {
        return JsonSerializer.Serialize(obj, new JsonSerializerOptions
        {
            WriteIndented = true
        });
    }

    public T? DeserializeFromJson<T>(string json)
    {
        return JsonSerializer.Deserialize<T>(json);
    }

    public async Task<T?> LoadFromJsonFileAsync<T>(string filePath)
    {
        var json = await File.ReadAllTextAsync(filePath);
        return DeserializeFromJson<T>(json);
    }

    public async Task SaveToJsonFileAsync<T>(string filePath, T obj)
    {
        var json = SerializeToJson(obj);
        await File.WriteAllTextAsync(filePath, json);
    }
}

// Thread-safe collections and operations
public class ThreadSafeCounter
{
    private long _count = 0;
    private readonly object _lock = new object();

    public long Count
    {
        get
        {
            lock (_lock)
            {
                return _count;
            }
        }
    }

    public void Increment()
    {
        Interlocked.Increment(ref _count);
    }

    public void Decrement()
    {
        Interlocked.Decrement(ref _count);
    }

    public void Add(long value)
    {
        Interlocked.Add(ref _count, value);
    }

    public void Reset()
    {
        Interlocked.Exchange(ref _count, 0);
    }
}

// Memory and performance utilities
public class PerformanceMonitor
{
    public TimeSpan MeasureExecutionTime(Action action)
    {
        var stopwatch = System.Diagnostics.Stopwatch.StartNew();
        action();
        stopwatch.Stop();
        return stopwatch.Elapsed;
    }

    public async Task<TimeSpan> MeasureExecutionTimeAsync(Func<Task> asyncAction)
    {
        var stopwatch = System.Diagnostics.Stopwatch.StartNew();
        await asyncAction();
        stopwatch.Stop();
        return stopwatch.Elapsed;
    }

    public long GetMemoryUsage()
    {
        GC.Collect();
        GC.WaitForPendingFinalizers();
        GC.Collect();
        return GC.GetTotalMemory(false);
    }

    public void ForceGarbageCollection()
    {
        GC.Collect();
        GC.WaitForPendingFinalizers();
        GC.Collect();
    }
}
