const fs = require('fs').promises;
const crypto = require('crypto');
const zlib = require('zlib');
const { promisify } = require('util');
const { Worker, isMainThread, parentPort, workerData } = require('worker_threads');

// Event Emitter for calculator
class EventEmitter {
    constructor() {
        this.events = {};
    }

    on(event, callback) {
        if (!this.events[event]) {
            this.events[event] = [];
        }
        this.events[event].push(callback);
    }

    emit(event, data) {
        if (this.events[event]) {
            this.events[event].forEach(callback => callback(data));
        }
    }
}

class AdvancedCalculator extends EventEmitter {
    constructor() {
        super();
        this.history = [];
    }

    add(a, b) {
        const result = a + b;
        const event = {
            operation: 'add',
            operands: [a, b],
            result,
            timestamp: Date.now()
        };
        this.history.push(event);
        this.emit('calculation', event);
        return result;
    }

    multiply(a, b) {
        const result = a * b;
        const event = {
            operation: 'multiply',
            operands: [a, b],
            result,
            timestamp: Date.now()
        };
        this.history.push(event);
        this.emit('calculation', event);
        return result;
    }

    getHistory() {
        return [...this.history];
    }

    clearHistory() {
        this.history = [];
        this.emit('historyCleared');
    }
}

// Async file operations
class FileProcessor {
    static async readFileAsync(filePath) {
        try {
            return await fs.readFile(filePath, 'utf8');
        } catch (error) {
            throw new Error(`Failed to read file ${filePath}: ${error.message}`);
        }
    }

    static async writeFileAsync(filePath, content) {
        try {
            await fs.writeFile(filePath, content, 'utf8');
        } catch (error) {
            throw new Error(`Failed to write file ${filePath}: ${error.message}`);
        }
    }

    static async readBinaryFileAsync(filePath) {
        try {
            return await fs.readFile(filePath);
        } catch (error) {
            throw new Error(`Failed to read binary file ${filePath}: ${error.message}`);
        }
    }

    static async writeBinaryFileAsync(filePath, data) {
        try {
            await fs.writeFile(filePath, data);
        } catch (error) {
            throw new Error(`Failed to write binary file ${filePath}: ${error.message}`);
        }
    }

    static async fileExists(filePath) {
        try {
            await fs.access(filePath);
            return true;
        } catch {
            return false;
        }
    }
}

// Binary data processing
class BinaryProcessor {
    static encodeString(text, encoding = 'utf8') {
        return Buffer.from(text, encoding);
    }

    static decodeBuffer(buffer, encoding = 'utf8') {
        return buffer.toString(encoding);
    }

    static async compressData(data) {
        const gzip = promisify(zlib.gzip);
        return await gzip(data);
    }

    static async decompressData(compressedData) {
        const gunzip = promisify(zlib.gunzip);
        return await gunzip(compressedData);
    }

    static calculateHash(data, algorithm = 'sha256') {
        const hash = crypto.createHash(algorithm);
        hash.update(data);
        return hash.digest('hex');
    }

    static generateRandomBytes(size) {
        return crypto.randomBytes(size);
    }
}

// HTTP utilities (using built-in modules)
class HttpClient {
    static async downloadString(url, timeout = 30000) {
        const https = require('https');
        const http = require('http');
        
        return new Promise((resolve, reject) => {
            const client = url.startsWith('https:') ? https : http;
            const timeoutId = setTimeout(() => {
                reject(new Error(`Request timeout after ${timeout}ms`));
            }, timeout);

            const req = client.get(url, (res) => {
                clearTimeout(timeoutId);
                let data = '';
                
                res.on('data', chunk => {
                    data += chunk;
                });
                
                res.on('end', () => {
                    if (res.statusCode >= 200 && res.statusCode < 300) {
                        resolve(data);
                    } else {
                        reject(new Error(`HTTP ${res.statusCode}: ${res.statusMessage}`));
                    }
                });
            });

            req.on('error', (error) => {
                clearTimeout(timeoutId);
                reject(error);
            });
        });
    }
}

// Parallel processing utilities
class ParallelProcessor {
    static async parallelMap(items, asyncFn, concurrency = 4) {
        const results = [];
        const executing = [];

        for (const item of items) {
            const promise = asyncFn(item).then(result => {
                executing.splice(executing.indexOf(promise), 1);
                return result;
            });

            results.push(promise);
            executing.push(promise);

            if (executing.length >= concurrency) {
                await Promise.race(executing);
            }
        }

        return Promise.all(results);
    }

    static async calculateSumParallel(numbers, chunkSize = 1000) {
        const chunks = [];
        for (let i = 0; i < numbers.length; i += chunkSize) {
            chunks.push(numbers.slice(i, i + chunkSize));
        }

        const chunkSums = await Promise.all(
            chunks.map(async chunk => {
                // Simulate async work
                await new Promise(resolve => setImmediate(resolve));
                return chunk.reduce((sum, num) => sum + num, 0);
            })
        );

        return chunkSums.reduce((total, sum) => total + sum, 0);
    }

    static async processWithWorkers(data, workerScript) {
        return new Promise((resolve, reject) => {
            const worker = new Worker(workerScript, {
                workerData: data
            });

            worker.on('message', resolve);
            worker.on('error', reject);
            worker.on('exit', (code) => {
                if (code !== 0) {
                    reject(new Error(`Worker stopped with exit code ${code}`));
                }
            });
        });
    }
}

// Thread-safe counter using atomic operations simulation
class ThreadSafeCounter {
    constructor() {
        this.value = 0;
        this.lock = Promise.resolve();
    }

    async increment() {
        this.lock = this.lock.then(async () => {
            await new Promise(resolve => setImmediate(resolve));
            this.value++;
        });
        await this.lock;
    }

    async decrement() {
        this.lock = this.lock.then(async () => {
            await new Promise(resolve => setImmediate(resolve));
            this.value--;
        });
        await this.lock;
    }

    getValue() {
        return this.value;
    }

    reset() {
        this.value = 0;
    }
}

// JSON data processing
class DataProcessor {
    static serializeToJson(obj, indent = 2) {
        return JSON.stringify(obj, null, indent);
    }

    static deserializeFromJson(jsonStr) {
        try {
            return JSON.parse(jsonStr);
        } catch (error) {
            throw new Error(`Invalid JSON: ${error.message}`);
        }
    }

    static async saveToJsonFile(filePath, obj) {
        const jsonStr = this.serializeToJson(obj);
        await FileProcessor.writeFileAsync(filePath, jsonStr);
    }

    static async loadFromJsonFile(filePath) {
        const jsonStr = await FileProcessor.readFileAsync(filePath);
        return this.deserializeFromJson(jsonStr);
    }
}

// Performance monitoring
class PerformanceMonitor {
    static measureExecutionTime(fn, ...args) {
        const start = process.hrtime.bigint();
        const result = fn(...args);
        const end = process.hrtime.bigint();
        const executionTime = Number(end - start) / 1000000; // Convert to milliseconds
        return { result, executionTime };
    }

    static async measureAsyncExecutionTime(asyncFn, ...args) {
        const start = process.hrtime.bigint();
        const result = await asyncFn(...args);
        const end = process.hrtime.bigint();
        const executionTime = Number(end - start) / 1000000; // Convert to milliseconds
        return { result, executionTime };
    }

    static getMemoryUsage() {
        return process.memoryUsage();
    }

    static forceGarbageCollection() {
        if (global.gc) {
            global.gc();
        } else {
            console.warn('Garbage collection not available. Run with --expose-gc flag.');
        }
    }
}

// Async utilities
async function asyncFactorial(n) {
    if (n <= 1) return 1;
    await new Promise(resolve => setImmediate(resolve)); // Simulate async work
    return n * await asyncFactorial(n - 1);
}

async function asyncFibonacci(n, memo = {}) {
    if (n in memo) return memo[n];
    if (n <= 1) return n;
    
    await new Promise(resolve => setImmediate(resolve)); // Simulate async work
    
    memo[n] = await asyncFibonacci(n - 1, memo) + await asyncFibonacci(n - 2, memo);
    return memo[n];
}

// Stream processing
class StreamProcessor {
    static async processLargeFile(filePath, processor) {
        const fs = require('fs');
        const readline = require('readline');
        
        const fileStream = fs.createReadStream(filePath);
        const rl = readline.createInterface({
            input: fileStream,
            crlfDelay: Infinity
        });

        const results = [];
        for await (const line of rl) {
            const result = await processor(line);
            results.push(result);
        }

        return results;
    }
}

module.exports = {
    AdvancedCalculator,
    FileProcessor,
    BinaryProcessor,
    HttpClient,
    ParallelProcessor,
    ThreadSafeCounter,
    DataProcessor,
    PerformanceMonitor,
    StreamProcessor,
    asyncFactorial,
    asyncFibonacci
};
