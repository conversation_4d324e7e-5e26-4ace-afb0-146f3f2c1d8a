import java.io.*;
import java.nio.file.*;
import java.util.*;
import java.util.concurrent.*;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * Advanced features test class
 */
public class AdvancedFeaturesTest {
    
    private static int testCount = 0;
    private static int passedTests = 0;
    
    public static void main(String[] args) {
        System.out.println("🚀 Running Advanced Java Tests...\n");
        
        // Advanced Calculator Tests
        System.out.println("🧮 Advanced Calculator Tests:");
        testAdvancedCalculator();
        
        // File Processing Tests
        System.out.println("\n📁 File Processing Tests:");
        testFileProcessing();
        
        // Binary Processing Tests
        System.out.println("\n🔢 Binary Processing Tests:");
        testBinaryProcessing();
        
        // Parallel Processing Tests
        System.out.println("\n⚡ Parallel Processing Tests:");
        testParallelProcessing();
        
        // Thread-Safe Counter Tests
        System.out.println("\n🔒 Thread-Safe Counter Tests:");
        testThreadSafeCounter();
        
        // Performance Monitoring Tests
        System.out.println("\n⏱️ Performance Monitoring Tests:");
        testPerformanceMonitoring();
        
        // Async Function Tests
        System.out.println("\n🔄 Async Function Tests:");
        testAsyncFunctions();
        
        // Summary
        System.out.println("\n🎉 Advanced Java tests completed!");
        System.out.println("Tests passed: " + passedTests + "/" + testCount);
        
        if (passedTests == testCount) {
            System.out.println("🎊 All tests passed!");
        } else {
            System.out.println("❌ Some tests failed.");
        }
    }
    
    private static void testAdvancedCalculator() {
        test("should emit events on calculation", () -> {
            AdvancedCalculator calc = new AdvancedCalculator();
            AtomicInteger eventCount = new AtomicInteger(0);
            
            calc.addListener((operation, result, timestamp) -> {
                eventCount.incrementAndGet();
                assertEquals("add", operation);
                assertEquals(8.0, result);
            });
            
            double result = calc.add(5, 3);
            assertEquals(8.0, result);
            assertEquals(1, eventCount.get());
        });
        
        test("should track calculation history", () -> {
            AdvancedCalculator calc = new AdvancedCalculator();
            
            calc.add(5, 3);
            calc.multiply(4, 2);
            
            List<AdvancedCalculator.CalculationEvent> history = calc.getHistory();
            assertEquals(2, history.size());
            assertEquals("add", history.get(0).operation);
            assertEquals("multiply", history.get(1).operation);
        });
    }
    
    private static void testFileProcessing() {
        test("should handle async file operations", () -> {
            try {
                Path tempFile = Files.createTempFile("test", ".txt");
                String testContent = "Hello, async world! 测试内容";
                
                // Test async write and read
                FileProcessor.writeFileAsync(tempFile.toString(), testContent)
                    .thenCompose(v -> FileProcessor.readFileAsync(tempFile.toString()))
                    .thenAccept(readContent -> {
                        assertEquals(testContent, readContent);
                    })
                    .get(5, TimeUnit.SECONDS);
                
                Files.deleteIfExists(tempFile);
            } catch (Exception e) {
                throw new RuntimeException(e);
            }
        });
        
        test("should handle binary file operations", () -> {
            try {
                Path tempFile = Files.createTempFile("test", ".bin");
                byte[] testData = "Binary test data \u0000\u0001\u0002\u0003".getBytes();
                
                FileProcessor.writeBinaryFile(tempFile.toString(), testData);
                byte[] readData = FileProcessor.readBinaryFile(tempFile.toString());
                
                assertArrayEquals(testData, readData);
                Files.deleteIfExists(tempFile);
            } catch (Exception e) {
                throw new RuntimeException(e);
            }
        });
    }
    
    private static void testBinaryProcessing() {
        test("should encode and decode strings", () -> {
            try {
                String testString = "Hello, 世界! 🌍";
                
                byte[] encoded = BinaryProcessor.encodeString(testString);
                String decoded = BinaryProcessor.decodeBytes(encoded);
                
                assertEquals(testString, decoded);
            } catch (Exception e) {
                throw new RuntimeException(e);
            }
        });
        
        test("should compress and decompress data", () -> {
            try {
                String testData = "This is test data for compression. ".repeat(100);
                byte[] originalData = testData.getBytes();
                
                byte[] compressed = BinaryProcessor.compressData(originalData);
                byte[] decompressed = BinaryProcessor.decompressData(compressed);
                
                assertArrayEquals(originalData, decompressed);
                assertTrue(compressed.length < originalData.length);
            } catch (Exception e) {
                throw new RuntimeException(e);
            }
        });
        
        test("should calculate hash correctly", () -> {
            try {
                byte[] testData1 = "Test data".getBytes();
                byte[] testData2 = "Test data".getBytes();
                byte[] testData3 = "Different data".getBytes();
                
                String hash1 = BinaryProcessor.calculateHash(testData1, "SHA-256");
                String hash2 = BinaryProcessor.calculateHash(testData2, "SHA-256");
                String hash3 = BinaryProcessor.calculateHash(testData3, "SHA-256");
                
                assertEquals(hash1, hash2);
                assertFalse(hash1.equals(hash3));
                assertEquals(64, hash1.length()); // SHA256 produces 64 hex characters
            } catch (Exception e) {
                throw new RuntimeException(e);
            }
        });
    }
    
    private static void testParallelProcessing() {
        test("should process items in parallel", () -> {
            List<Integer> numbers = Arrays.asList(1, 2, 3, 4, 5);
            
            List<Integer> results = ParallelProcessor.parallelMap(numbers, x -> {
                try {
                    Thread.sleep(10); // Simulate work
                } catch (InterruptedException e) {
                    Thread.currentThread().interrupt();
                }
                return x * x;
            });
            
            List<Integer> expected = Arrays.asList(1, 4, 9, 16, 25);
            assertEquals(expected, results);
        });
        
        test("should calculate sum in parallel", () -> {
            List<Integer> numbers = new ArrayList<>();
            for (int i = 1; i <= 1000; i++) {
                numbers.add(i);
            }
            
            long expectedSum = numbers.stream().mapToLong(Integer::longValue).sum();
            long parallelSum = ParallelProcessor.calculateSumParallel(numbers);
            
            assertEquals(expectedSum, parallelSum);
        });
        
        test("should handle async parallel operations", () -> {
            try {
                List<Integer> numbers = Arrays.asList(1, 2, 3, 4, 5);
                
                CompletableFuture<List<Integer>> futureResults = ParallelProcessor.parallelMapAsync(
                    numbers, 
                    x -> CompletableFuture.supplyAsync(() -> {
                        try {
                            Thread.sleep(10);
                        } catch (InterruptedException e) {
                            Thread.currentThread().interrupt();
                        }
                        return x * x;
                    })
                );
                
                List<Integer> results = futureResults.get(5, TimeUnit.SECONDS);
                List<Integer> expected = Arrays.asList(1, 4, 9, 16, 25);
                assertEquals(expected, results);
            } catch (Exception e) {
                throw new RuntimeException(e);
            }
        });
    }
    
    private static void testThreadSafeCounter() {
        test("should handle concurrent operations", () -> {
            try {
                ThreadSafeCounter counter = new ThreadSafeCounter();
                int numThreads = 10;
                int operationsPerThread = 1000;
                
                ExecutorService executor = Executors.newFixedThreadPool(numThreads);
                List<Future<?>> futures = new ArrayList<>();
                
                for (int i = 0; i < numThreads; i++) {
                    futures.add(executor.submit(() -> {
                        for (int j = 0; j < operationsPerThread; j++) {
                            counter.increment();
                        }
                    }));
                }
                
                for (Future<?> future : futures) {
                    future.get(5, TimeUnit.SECONDS);
                }
                
                executor.shutdown();
                
                long expectedValue = (long) numThreads * operationsPerThread;
                assertEquals(expectedValue, counter.getValue());
            } catch (Exception e) {
                throw new RuntimeException(e);
            }
        });
        
        test("should handle basic counter operations", () -> {
            ThreadSafeCounter counter = new ThreadSafeCounter();
            
            counter.increment();
            counter.increment();
            assertEquals(2L, counter.getValue());
            
            counter.decrement();
            assertEquals(1L, counter.getValue());
            
            counter.add(5);
            assertEquals(6L, counter.getValue());
            
            counter.reset();
            assertEquals(0L, counter.getValue());
        });
    }
    
    private static void testPerformanceMonitoring() {
        test("should measure execution time", () -> {
            PerformanceMonitor.TimedResult<String> result = PerformanceMonitor.measureExecutionTime(() -> {
                try {
                    Thread.sleep(50);
                } catch (InterruptedException e) {
                    Thread.currentThread().interrupt();
                }
                return "result";
            });
            
            assertEquals("result", result.result);
            assertTrue(result.executionTimeMs >= 40);
        });
        
        test("should get memory usage", () -> {
            long memoryUsage = PerformanceMonitor.getMemoryUsage();
            assertTrue(memoryUsage > 0);
        });
    }
    
    private static void testAsyncFunctions() {
        test("should calculate async factorial", () -> {
            try {
                CompletableFuture<Long> future = AsyncUtils.asyncFactorial(5);
                Long result = future.get(5, TimeUnit.SECONDS);
                assertEquals(120L, result.longValue());
                
                CompletableFuture<Long> future0 = AsyncUtils.asyncFactorial(0);
                Long result0 = future0.get(5, TimeUnit.SECONDS);
                assertEquals(1L, result0.longValue());
            } catch (Exception e) {
                throw new RuntimeException(e);
            }
        });
        
        test("should calculate async fibonacci", () -> {
            try {
                CompletableFuture<Long> future = AsyncUtils.asyncFibonacci(5);
                Long result = future.get(5, TimeUnit.SECONDS);
                assertEquals(5L, result.longValue());
                
                CompletableFuture<Long> future0 = AsyncUtils.asyncFibonacci(0);
                Long result0 = future0.get(5, TimeUnit.SECONDS);
                assertEquals(0L, result0.longValue());
            } catch (Exception e) {
                throw new RuntimeException(e);
            }
        });
    }
    
    // Test utility methods
    private static void test(String description, Runnable testCase) {
        testCount++;
        try {
            testCase.run();
            System.out.println("✅ " + description);
            passedTests++;
        } catch (Exception e) {
            System.out.println("❌ " + description + ": " + e.getMessage());
        }
    }
    
    private static void assertEquals(Object expected, Object actual) {
        if (!Objects.equals(expected, actual)) {
            throw new AssertionError("Expected " + expected + " but got " + actual);
        }
    }
    
    private static void assertEquals(double expected, double actual) {
        if (Math.abs(expected - actual) > 0.0001) {
            throw new AssertionError("Expected " + expected + " but got " + actual);
        }
    }
    
    private static void assertEquals(long expected, long actual) {
        if (expected != actual) {
            throw new AssertionError("Expected " + expected + " but got " + actual);
        }
    }
    
    private static void assertTrue(boolean condition) {
        if (!condition) {
            throw new AssertionError("Expected true but got false");
        }
    }
    
    private static void assertFalse(boolean condition) {
        if (condition) {
            throw new AssertionError("Expected false but got true");
        }
    }
    
    private static void assertArrayEquals(byte[] expected, byte[] actual) {
        if (!Arrays.equals(expected, actual)) {
            throw new AssertionError("Arrays are not equal");
        }
    }
}
